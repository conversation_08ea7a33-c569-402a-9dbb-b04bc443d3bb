{"name": "range-parser", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "description": "Range header field string parser", "version": "1.2.1", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["range", "parser", "http"], "repository": "jshttp/range-parser", "devDependencies": {"deep-equal": "1.0.1", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.1.1"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}}